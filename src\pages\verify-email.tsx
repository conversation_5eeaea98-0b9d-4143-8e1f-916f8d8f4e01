
import { SignUp } from "@clerk/clerk-react";

export default function VerifyEmailPage() {
  return (
    <div className="flex justify-center items-center h-screen bg-gray-50">
      <div className="w-full max-w-md">
        <SignUp
          path="/verify-email"
          routing="path"
          signInUrl="/sign-in"
          unsafeMetadata={{ userType: "admin" }}
          appearance={{
            elements: {
              rootBox: "w-full",
              card: "shadow-none border-none bg-white p-6 rounded-2xl",
              headerTitle: "text-xl font-semibold text-gray-800 text-center",
              headerSubtitle: "hidden", // removes "Already have an account?"
              formButtonPrimary:
                "w-full py-2 mt-4 rounded-lg bg-blue-600 hover:bg-blue-700 text-white",
              formFieldInput:
                "w-full px-3 py-2 border rounded-lg focus:ring focus:ring-blue-300",
              formFieldLabel: "text-sm font-medium text-gray-700",
              footer: "hidden", // hides Clerk’s footer links
            },
            layout: {
              socialButtonsPlacement: "bottom",
              socialButtonsVariant: "blockButton",
            },
          }}
        />
      </div>
    </div>
  );
}
