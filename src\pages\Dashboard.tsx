import { useUser } from "@clerk/clerk-react";

export default function Dashboard() {
  const { user } = useUser();

  if (!user) {
    return <div>Loading...</div>;
  }

  // Example: get the role from public metadata
  const role = user.publicMetadata?.role as string | undefined;

  return (
    <div className="flex justify-center items-center h-screen">
      {role === "designer" && <div>Welcome Designer Dashboard</div>}
      {role === "client" && <div>Welcome Client Dashboard</div>}
      {role === "admin" && <div>Welcome Admin Dashboard</div>}
      {!role && <div>No role assigned</div>}
    </div>
  );
}
