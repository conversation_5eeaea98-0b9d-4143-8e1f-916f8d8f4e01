// convex/users.ts
import { mutation, query } from "./_generated/server"; 
import { v } from "convex/values";

export const syncUser = mutation({
  args: {
    clerkId: v.string(),
    email: v.string(),
    role: v.string(),
    firstName: v.string(),
    lastName: v.string(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("users")
      .withIndex("by_clerkId", (q) => q.eq("clerkId", args.clerkId))
      .unique();

    if (existing) {
      await ctx.db.patch(existing._id, {
        email: args.email,
        role: args.role,
        firstName: args.firstName,
        lastName: args.lastName,
      });
      return existing._id;
    } else {
      return await ctx.db.insert("users", {
        ...args,
        createdAt: Date.now(),
      });
    }
  },
});
