import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import LandingPage from "./pages/LandingPage";
import LoginPage from "./pages/LoginPage";
import AdminSignup from "./pages/AdminSignup";
import ClientSignup from "./pages/ClientSignup";
import DesignerSignup from "./pages/DesignerSignup";
import DesignerDashboard from "./pages/DesignerDashboard";
import ClientDashboard from "./pages/ClientDashboard";
import AdminDashboard from "./pages/AdminDashboard";
import VerifyEmailPage from "./pages/verify-email";



function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/signup/client" element={<ClientSignup />} />
        <Route path="/signup/designer" element={<DesignerSignup />} />
        <Route path="/signup/admin" element={<AdminSignup />} />
        <Route path="/designer" element={<DesignerDashboard />} />
        <Route path="/client" element={<ClientDashboard />} />
        <Route path="/admin" element={<AdminDashboard />} />
        <Route path="/verify-email" element={<VerifyEmailPage />} />
           </Routes>
    </Router>
  );
}

export default App;
