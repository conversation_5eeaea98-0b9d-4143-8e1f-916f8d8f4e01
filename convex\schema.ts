import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    clerkId: v.string(), // Clerk user ID
    email: v.string(),
    role: v.string(), // "client", "designer", "admin"
    firstName: v.string(),
    lastName: v.string(),
    createdAt: v.number(), // store Date.now()
  }).index("by_clerkId", ["clerkId"]),
});
