import { clerkClient } from "@clerk/clerk-sdk-node";

export async function promoteUserType(userId: string) {
  const user = await clerkClient.users.getUser(userId);

  // get role from unsafeMetadata.userType
  const userType = user.unsafeMetadata.userType as string | undefined;

  if (userType) {
    await clerkClient.users.updateUserMetadata(userId, {
      publicMetadata: { role: userType }, // set role here
      unsafeMetadata: {}, // clear it after promotion
    });
  }
}
