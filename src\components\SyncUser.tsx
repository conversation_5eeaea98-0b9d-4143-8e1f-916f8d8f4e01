import { useUser } from "@clerk/clerk-react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useEffect } from "react";

export default function SyncUser() {
  const { user } = useUser();
  const syncUser = useMutation(api.users.syncUser);

  useEffect(() => {
    if (user) {
      syncUser({
        clerkId: user.id,
        email: user.primaryEmailAddress?.emailAddress ?? "",
        role: user.publicMetadata?.role as string,
        firstName: user.firstName ?? "",
        lastName: user.lastName ?? "",
      });
    }
  }, [user]);

  return null;
}
